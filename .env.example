# API Key Configuration
# SECURITY: Never commit real API keys to version control
# Use strong, unique API keys for each environment (dev/staging/prod)

# Groq API Configuration (REQUIRED)
# Get your API key from: https://console.groq.com/
# Format: gsk_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GROQ_API_KEY=your_groq_api_key_here

# Azure Speech Services Configuration (OPTIONAL)
# Only needed if you want to use Azure STT instead of Groq STT
# Get your key from: https://portal.azure.com/
# Format: 32-character hexadecimal string
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=eastus

# TTS Provider Configuration
# Options: edge (FREE, recommended), groq (limited)
TTS_PROVIDER=edge

# Backend Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false

# Frontend Configuration
BACKEND_URL=http://localhost:8000

# SSL/HTTPS Configuration
# Set to true to enable HTTPS mode
USE_HTTPS=false
# SECURITY WARNING: Only set to true in development with self-signed certificates
# NEVER use this in production - it disables SSL certificate verification
ALLOW_SELF_SIGNED_CERTS=false

# Optional: Deployment Configuration
# For production deployment, set these appropriately
# CORS_ORIGINS=https://your-frontend-domain.com
# LOG_LEVEL=info
